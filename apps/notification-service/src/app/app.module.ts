import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { NotificationsController } from './controllers/notifications.controller';
import { HealthController } from './controllers/health.controller';
import { NotificationService } from './services/notification.service';
import { HealthService } from './services/health.service';
import { NotificationModule } from '@libs';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.development', '.env'],
    }),
    ScheduleModule.forRoot(),
    NotificationModule,
  ],
  controllers: [App<PERSON>ontroller, NotificationsController, HealthController],
  providers: [AppService, NotificationService, HealthService],
})
export class AppModule {}
