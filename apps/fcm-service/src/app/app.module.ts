import { Module } from '@nestjs/common';
import { AppService } from './app.service';
import { FcmProvider, NotificationModule, NotificationService } from '@libs';
import { INotificationProvider } from '@libs';
import { ConfigModule } from '@nestjs/config';
@Module({
  imports: [
    NotificationModule,
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.development', '.env'],
    }),
  ],
  providers: [
    {
      provide: NotificationService,
      useFactory: (provider: FcmProvider) => {
        console.log('provider', provider);
        return new NotificationService(provider);
      },
      inject: [FcmProvider],
    },
    AppService,
  ],
})
export class AppModule {}
