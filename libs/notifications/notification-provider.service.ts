import { Inject, Logger } from '@nestjs/common';
import { NotificationChannel } from '../shared/enums';
import {
  INotificationProvider,
  type INotificationMessage,
  type NotificationRecipient,
  type NotificationResult,
} from '../shared/interfaces';

export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(private readonly provider: INotificationProvider<any>) {
    console.log('NotificationService initialized with provider:', provider.channel);
  }

  async sendNotification(payload: INotificationMessage): Promise<NotificationResult> {
    try {
      return await this.provider.send(payload);
    } catch (error) {
      this.logger.error(`Failed to send notification via ${payload.channel}`, error);
      throw error;
    }
  }

  //   async sendBulkNotifications(payload: INotificationMessage): Promise<NotificationResult[]> {
  //     const results: NotificationResult[] = [];

  //     const recipients = payload.getRecipient();
  //     // Group recipients by channel for efficient processing
  //     const recipientsByChannel = this.groupRecipientsByChannel(recipients);

  //     for (const [channel, channelRecipients] of recipientsByChannel) {
  //       // Send notifications concurrently for each channel
  //       const channelPromises = channelRecipients.map(recipient =>
  //         this.provider.send(payload).catch(error => {
  //           this.logger.error(`Failed to send to ${recipient.id}`, error);
  //           return {
  //             id: `error-${recipient.id}`,
  //             recipientId: recipient.id,
  //             channel: recipient.channel,
  //             status: 'failed' as any,
  //             failureReason: error.message,
  //           } as NotificationResult;
  //         })
  //       );

  //       const channelResults = await Promise.all(channelPromises);
  //       results.push(...channelResults);
  //     }

  //     return results;
  //   }

  private groupRecipientsByChannel(
    recipients: NotificationRecipient[]
  ): Map<NotificationChannel, NotificationRecipient[]> {
    const grouped = new Map<NotificationChannel, NotificationRecipient[]>();

    for (const recipient of recipients) {
      if (!grouped.has(recipient.channel)) {
        grouped.set(recipient.channel, []);
      }
      grouped.get(recipient.channel)!.push(recipient);
    }

    return grouped;
  }

  async getHealthStatus(): Promise<boolean> {
    try {
      const isHealthy = await this.provider.isHealthy();
      return isHealthy;
    } catch (error) {
      this.logger.error(`Health check failed for ${this.provider.channel}`, error);
    }
    return false;
  }

  validateRecipient(recipient: NotificationRecipient): boolean {
    return this.provider.validateRecipient(recipient);
  }

  validateRecipients(recipients: NotificationRecipient[]): {
    valid: NotificationRecipient[];
    invalid: NotificationRecipient[];
  } {
    const valid: NotificationRecipient[] = [];
    const invalid: NotificationRecipient[] = [];

    for (const recipient of recipients) {
      if (this.validateRecipient(recipient)) {
        valid.push(recipient);
      } else {
        invalid.push(recipient);
      }
    }

    return { valid, invalid };
  }
}
