import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import type {
  NotificationPayload,
  NotificationRecipient,
  NotificationResult,
} from '../../../shared/interfaces/notification.interface';
import { NotificationProvider } from '../notification.provider';

@Injectable()
export class EmailProvider extends NotificationProvider<EmailMessage> {
  readonly channel = NotificationChannel.EMAIL;
  private transporter: nodemailer.Transporter;

  constructor(private configService: ConfigService) {
    super();
    this.initializeTransporter();
  }

  private initializeTransporter(): void {
    try {
      const host = this.configService.get<string>('EMAIL_HOST');
      const port = this.configService.get<number>('EMAIL_PORT');
      const secure = this.configService.get<boolean>('EMAIL_SECURE');
      const user = this.configService.get<string>('EMAIL_USER');
      const pass = this.configService.get<string>('EMAIL_PASS');

      if (!host || !port || !user || !pass) {
        this.logger.warn('Email configuration is incomplete. Email provider will be disabled.');
        return;
      }

      this.transporter = nodemailer.createTransport({
        host,
        port,
        secure,
        auth: {
          user,
          pass,
        },
        pool: true,
        maxConnections: 5,
        maxMessages: 100,
        rateDelta: 1000,
        rateLimit: 5,
      });

      this.logger.log('Email transporter initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize email transporter', error);
    }
  }

  async send(
    payload: NotificationPayload,
    recipient: NotificationRecipient
  ): Promise<NotificationResult> {
    try {
      if (!this.transporter) {
        throw new Error('Email transporter not initialized');
      }

      if (!this.validatePayload(payload) || !this.validateRecipient(recipient)) {
        throw new Error('Invalid payload or recipient');
      }

      const sanitizedPayload = this.sanitizePayload(payload);
      const fromEmail = this.configService.get<string>('EMAIL_FROM');

      const mailOptions: nodemailer.SendMailOptions = {
        from: fromEmail,
        to: recipient.address,
        subject: sanitizedPayload.title,
        text: sanitizedPayload.body,
        html: this.generateHtmlContent(sanitizedPayload),
      };

      // Add attachments if image URL is provided
      if (sanitizedPayload.imageUrl) {
        mailOptions.attachments = [
          {
            filename: 'image.png',
            path: sanitizedPayload.imageUrl,
            cid: 'notification-image',
          },
        ];
      }

      const info = await this.transporter.sendMail(mailOptions);

      this.logger.log(`Email notification sent successfully: ${info.messageId}`);

      return this.createResult(recipient.id, NotificationStatus.SENT, {
        messageId: info.messageId,
        emailAddress: recipient.address,
        response: info.response,
      });
    } catch (error) {
      return this.handleError(error, recipient.id);
    }
  }

  private generateHtmlContent(payload: NotificationPayload): string {
    let html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333; margin-bottom: 20px;">${payload.title}</h2>
        <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">${payload.body}</p>
    `;

    if (payload.imageUrl) {
      html += `<img src="cid:notification-image" alt="Notification Image" style="max-width: 100%; height: auto; margin-bottom: 20px;">`;
    }

    if (payload.actionUrl) {
      html += `
        <div style="text-align: center; margin: 30px 0;">
          <a href="${payload.actionUrl}" 
             style="background-color: #2196F3; color: white; padding: 12px 24px; 
                    text-decoration: none; border-radius: 4px; display: inline-block;">
            View Details
          </a>
        </div>
      `;
    }

    html += `
        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        <p style="color: #999; font-size: 12px; text-align: center;">
          This is an automated notification. Please do not reply to this email.
        </p>
      </div>
    `;

    return html;
  }

  validateRecipient(recipient: NotificationRecipient): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return (
      recipient.channel === NotificationChannel.EMAIL &&
      !!recipient.address &&
      emailRegex.test(recipient.address)
    );
  }

  isConfigured(): boolean {
    return !!this.transporter;
  }

  async testConnection(): Promise<boolean> {
    return await this.isHealthy();
  }

  async isHealthy(): Promise<boolean> {
    try {
      if (!this.transporter) {
        return false;
      }

      await this.transporter.verify();
      return true;
    } catch (error) {
      this.logger.error('Email health check failed', error);
      return false;
    }
  }

  async shutdown(): Promise<void> {
    if (this.transporter) {
      this.transporter.close();
      this.logger.log('Email transporter closed');
    }
  }
}
